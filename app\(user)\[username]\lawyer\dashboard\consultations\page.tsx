'use client';

import { LawyerConsultationsList } from '@/components/lawyer/LawyerConsultationsList';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useLawyers } from '@/lib/hooks';
import { userStore } from '@/lib/store/user';
import { ArrowLeft } from 'lucide-react';
import { useParams, useRouter } from 'next/navigation';
import { useEffect } from 'react';
import { toast } from 'sonner';

export default function LawyerConsultationsPage() {
  const params = useParams();
  const username = params.username as string;
  const router = useRouter();
  const { profile } = userStore();

  const {
    loading,
    isLawyer,
    lawyerProfile,
    lawyerConsultations,
    fetchLawyerProfile,
    fetchLawyerConsultations,
  } = useLawyers();

  useEffect(() => {
    if (profile && profile.user_role !== 'lawyer') {
      toast.error('Access denied', {
        description: 'You need to be a lawyer to access this page',
      });
      router.push(`/${username}`);
    } else if (profile) {
      // Only fetch if we have a profile
      fetchLawyerProfile().catch((error) => {
        console.error('Error fetching lawyer profile:', error);
        // Don't show error to user, just log it
      });
    }
  }, [profile, username, router, fetchLawyerProfile]);

  useEffect(() => {
    if (lawyerProfile?.id) {
      fetchLawyerConsultations(lawyerProfile.id).catch((error) => {
        console.error('Error fetching lawyer consultations:', error);
        // Don't show error to user, just log it
      });
    }
  }, [lawyerProfile, fetchLawyerConsultations]);

  if (loading) {
    return (
      <div className="p-6">
        <h1 className="text-2xl font-bold mb-4">Lawyer Consultations</h1>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-center h-40">
              <div className="animate-pulse flex space-x-4">
                <div className="flex-1 space-y-6 py-1">
                  <div className="h-2 bg-slate-200 rounded"></div>
                  <div className="space-y-3">
                    <div className="grid grid-cols-3 gap-4">
                      <div className="h-2 bg-slate-200 rounded col-span-2"></div>
                      <div className="h-2 bg-slate-200 rounded col-span-1"></div>
                    </div>
                    <div className="h-2 bg-slate-200 rounded"></div>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="flex flex-col md:flex-row items-start md:items-center justify-between mb-6 gap-4">
        <div>
          <div className="flex items-center gap-2 mb-2">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => router.push(`/${username}/lawyer/dashboard`)}
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <h1 className="text-2xl font-bold">Lawyer Consultations</h1>
          </div>
          <p className="text-muted-foreground">
            Manage all your client consultations
          </p>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>All Consultations</CardTitle>
        </CardHeader>
        <CardContent>
          <LawyerConsultationsList
            consultations={lawyerConsultations}
            emptyMessage="No consultations found"
          />
        </CardContent>
      </Card>
    </div>
  );
}
