'use client';

import { LawyerAvailabilitySettings } from '@/components/lawyer/LawyerAvailabilitySettings';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { userStore } from '@/lib/store/user';
import { ArrowLeft, Calendar } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';

export default function LawyerAvailabilityPage() {
  const router = useRouter();
  const { profile } = userStore();
  const [isLawyer, setIsLawyer] = useState<boolean | null>(null);

  useEffect(() => {
    // Check if the user is a lawyer
    if (profile) {
      setIsLawyer(profile.user_role === 'lawyer');

      // If not a lawyer, show error and redirect
      if (profile.user_role !== 'lawyer') {
        toast.error('Access denied', {
          description: 'You need to be a lawyer to access this page',
        });
        router.push(`/${profile.username}`);
      }
    }
  }, [profile, router]);

  // Show loading state while checking
  if (isLawyer === null) {
    return (
      <div className="p-6">
        <h1 className="text-2xl font-bold mb-4">Availability Settings</h1>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  // If not a lawyer, show access denied (this should not happen due to the redirect)
  if (!isLawyer) {
    return (
      <div className="p-6">
        <h1 className="text-2xl font-bold mb-4">Availability Settings</h1>
        <Card>
          <CardContent className="p-6">
            <div className="text-center">
              <Calendar className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <h2 className="text-xl font-semibold mb-2">Access Denied</h2>
              <p className="text-muted-foreground mb-4">
                You need to have a lawyer account to access this page.
              </p>
              <Button onClick={() => router.push(`/${profile?.username}`)}>
                Return to Home
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="flex flex-col md:flex-row items-start md:items-center justify-between mb-6 gap-4">
        <div>
          <h1 className="text-2xl font-bold">Availability Settings</h1>
          <p className="text-muted-foreground">
            Manage your availability for consultations
          </p>
        </div>
        <Button
          variant="outline"
          onClick={() => router.push(`/${profile?.username}/lawyer/dashboard`)}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Dashboard
        </Button>
      </div>

      <LawyerAvailabilitySettings />
    </div>
  );
}
