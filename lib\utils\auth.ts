import { Database } from '@/lib/supabase/database-types';
import { supabaseServer } from '../supabase/auth/server';

type UserRole = Database['public']['Enums']['user_role'];
type Permission = Database['public']['Enums']['permission'];

export async function checkUserPermission(
  permission: Permission
): Promise<boolean> {
  const supabase = await supabaseServer();

  const {
    data: { user },
  } = await supabase.auth.getUser();
  if (!user) return false;

  // Get user's role and associated permissions
  const { data: profile } = await supabase
    .from('profiles')
    .select('user_role')
    .eq('id', user.id)
    .single();

  if (!profile?.user_role) return false;

  const { data: role } = await supabase
    .from('roles')
    .select('permissions')
    .eq('name', profile.user_role)
    .single();

  return role?.permissions?.includes(permission) || false;
}

export async function getUserRole(): Promise<UserRole | null> {
  const supabase = await supabaseServer();

  const {
    data: { user },
  } = await supabase.auth.getUser();
  if (!user) return null;

  const { data: profile } = await supabase
    .from('profiles')
    .select('user_role')
    .eq('id', user.id)
    .single();

  return profile?.user_role || null;
}

export const DEFAULT_ROLE_PERMISSIONS: Record<UserRole, Permission[]> = {
  user: ['create_form', 'edit_form', 'delete_form', 'view_form'],
  lawyer: [
    'create_form',
    'edit_form',
    'delete_form',
    'view_form',
    'manage_users',
    'manage_roles',
  ],
};
