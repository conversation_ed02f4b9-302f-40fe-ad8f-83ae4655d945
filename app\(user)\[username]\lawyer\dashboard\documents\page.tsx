'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useLawyers } from '@/lib/hooks';
import { userStore } from '@/lib/store/user';
import { ArrowLeft, FileText } from 'lucide-react';
import { useParams, useRouter } from 'next/navigation';
import { useEffect } from 'react';
import { toast } from 'sonner';

export default function LawyerDocumentsPage() {
  const params = useParams();
  const username = params.username as string;
  const router = useRouter();
  const { profile } = userStore();

  const { loading, isLawyer, lawyerProfile, fetchLawyerProfile } = useLawyers();

  useEffect(() => {
    if (profile && profile.user_role !== 'lawyer') {
      toast.error('Access denied', {
        description: 'You need to be a lawyer to access this page',
      });
      router.push(`/${username}`);
    } else if (profile) {
      // Only fetch if we have a profile
      fetchLawyerProfile().catch((error) => {
        console.error('Error fetching lawyer profile:', error);
        // Don't show error to user, just log it
      });
    }
  }, [profile, username, router, fetchLawyerProfile]);

  if (loading) {
    return (
      <div className="p-6">
        <h1 className="text-2xl font-bold mb-4">Document Reviews</h1>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-center h-40">
              <div className="animate-pulse flex space-x-4">
                <div className="flex-1 space-y-6 py-1">
                  <div className="h-2 bg-slate-200 rounded"></div>
                  <div className="space-y-3">
                    <div className="grid grid-cols-3 gap-4">
                      <div className="h-2 bg-slate-200 rounded col-span-2"></div>
                      <div className="h-2 bg-slate-200 rounded col-span-1"></div>
                    </div>
                    <div className="h-2 bg-slate-200 rounded"></div>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="flex flex-col md:flex-row items-start md:items-center justify-between mb-6 gap-4">
        <div>
          <div className="flex items-center gap-2 mb-2">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => router.push(`/${username}/lawyer/dashboard`)}
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <h1 className="text-2xl font-bold">Document Reviews</h1>
          </div>
          <p className="text-muted-foreground">
            Review and manage client documents
          </p>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Documents for Review</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-12">
            <FileText className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium mb-2">No documents to review</h3>
            <p className="text-muted-foreground mb-4">
              Documents submitted for review will appear here.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
