'use client';

import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { userStore } from '@/lib/store/user';
import { supabaseClient } from '@/lib/supabase/client';
import { Gavel, User } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';

export function RoleSwitch() {
  const { profile, updateProfile } = userStore();
  const [isLawyer, setIsLawyer] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  useEffect(() => {
    if (profile) {
      setIsLawyer(profile.user_role === 'lawyer');
    }
  }, [profile]);

  const handleRoleToggle = async () => {
    if (!profile) return;

    setIsLoading(true);

    try {
      const newRole = isLawyer ? 'user' : 'lawyer';

      const { error } = await supabaseClient
        .from('profiles')
        .update({ user_role: newRole })
        .eq('id', profile.id);

      if (error) throw error;

      // Update local state
      setIsLawyer(!isLawyer);

      // Update profile in store
      updateProfile({
        ...profile,
        user_role: newRole,
      });

      toast.success(`Your account is now a ${newRole} account`);

      // If switching to lawyer, redirect to lawyer dashboard
      if (newRole === 'lawyer') {
        router.push(`/${profile.username}/lawyer/dashboard`);
      }
    } catch (error) {
      console.error('Error updating role:', error);
      toast.error('Failed to update role');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Account Type</CardTitle>
        <CardDescription>
          Switch between regular user and lawyer account types
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex flex-col space-y-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              {isLawyer ? (
                <div className="bg-blue-100 p-2 rounded-full">
                  <Gavel className="h-5 w-5 text-blue-600" />
                </div>
              ) : (
                <div className="bg-gray-100 p-2 rounded-full">
                  <User className="h-5 w-5 text-gray-600" />
                </div>
              )}
              <div>
                <p className="font-medium">
                  {isLawyer ? 'Lawyer Account' : 'Regular Account'}
                </p>
                <p className="text-sm text-muted-foreground">
                  {isLawyer
                    ? 'You can provide legal services and consultations'
                    : 'You can create documents and request legal consultations'}
                </p>
              </div>
            </div>
            <Switch
              checked={isLawyer}
              onCheckedChange={handleRoleToggle}
              disabled={isLoading}
            />
          </div>

          {isLawyer ? (
            <div className="bg-blue-50 p-4 rounded-md border border-blue-100">
              <h4 className="font-medium text-blue-800 mb-1">
                Lawyer Account Active
              </h4>
              <p className="text-sm text-blue-700 mb-3">
                You have access to the lawyer dashboard where you can manage
                consultations, client documents, and your lawyer profile.
              </p>
              <div className="flex flex-wrap gap-2">
                <Button
                  variant="outline"
                  className="bg-white border-blue-200 text-blue-700 hover:bg-blue-50"
                  onClick={() =>
                    router.push(`/${profile?.username}/lawyer/dashboard`)
                  }
                >
                  Go to Lawyer Dashboard
                </Button>
                <Button
                  variant="outline"
                  className="bg-white border-blue-200 text-blue-700 hover:bg-blue-50"
                  onClick={() =>
                    router.push(`/${profile?.username}/lawyer/clients`)
                  }
                >
                  Manage Clients
                </Button>
              </div>
            </div>
          ) : (
            <div className="bg-gray-50 p-4 rounded-md border border-gray-100">
              <h4 className="font-medium text-gray-800 mb-1">
                Regular Account Active
              </h4>
              <p className="text-sm text-gray-700 mb-3">
                You can create and manage documents, and request consultations
                with lawyers when needed.
              </p>
              <div className="flex flex-wrap gap-2">
                <Button
                  variant="outline"
                  className="bg-white border-gray-200 text-gray-700 hover:bg-gray-50"
                  onClick={() => router.push(`/${profile?.username}/documents`)}
                >
                  Manage Documents
                </Button>
                <Button
                  variant="outline"
                  className="bg-white border-gray-200 text-gray-700 hover:bg-gray-50"
                  onClick={() => router.push(`/${profile?.username}/lawyer`)}
                >
                  Find a Lawyer
                </Button>
              </div>
            </div>
          )}

          <div className="border-t pt-4">
            <h4 className="font-medium mb-2">About Account Types</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="p-3 rounded-md border">
                <div className="flex items-center gap-2 mb-2">
                  <User className="h-4 w-4 text-gray-600" />
                  <span className="font-medium">Regular User</span>
                </div>
                <ul className="text-sm text-muted-foreground space-y-1 list-disc pl-5">
                  <li>Create and manage legal documents</li>
                  <li>Request lawyer consultations</li>
                  <li>Share documents with collaborators</li>
                  <li>Access document templates</li>
                </ul>
              </div>

              <div className="p-3 rounded-md border">
                <div className="flex items-center gap-2 mb-2">
                  <Gavel className="h-4 w-4 text-blue-600" />
                  <span className="font-medium">Lawyer</span>
                </div>
                <ul className="text-sm text-muted-foreground space-y-1 list-disc pl-5">
                  <li>Provide legal consultations</li>
                  <li>Review client documents</li>
                  <li>Manage client relationships</li>
                  <li>Track earnings and consultations</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
