'use client';

import { But<PERSON>, buttonVariants } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { UserAvatar } from '@/components/ui/user-avatar';
import { FONT_BIRCOLAGE_GROTESQUE, FONT_JETBRAINS_MONO } from '@/lib/constants';
import useScroll from '@/lib/hooks/useScroll';
import { userStore } from '@/lib/store/user';
import { authService } from '@/lib/supabase/auth/auth-service';
import { cn } from '@/lib/utils';
import { Cog, DoorOpenIcon, HomeIcon } from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import React from 'react';
import { toast } from 'sonner';

export function Navbar() {
  const router = useRouter();
  const [open, setOpen] = React.useState(false);
  const { user, profile, removeProfile, removeUser } = userStore();
  const scrolled = useScroll(15);

  async function handleLogOut() {
    try {
      await authService.signOut();
      removeProfile(null);
      removeUser(null);
      router.refresh();
      return 'Done';
    } catch (error) {
      console.error('Error signing out:', error);
      throw error;
    }
  }

  return (
    <header
      className={cn(
        'fixed inset-x-0 top-0 z-50  flex justify-center border border-transparent px-4 py-3 transition duration-300',
        scrolled || open
          ? 'border-gray-200/50 bg-white/80 shadow-2xl shadow-black/5 backdrop-blur-sm'
          : 'bg-white/0'
      )}
    >
      <div className="max-w-4xl mx-auto w-full flex items-center justify-between">
        <div className="w-full md:my-auto">
          <div className="relative flex items-center justify-between">
            <Link
              href={'/'}
              aria-label="Home"
              className={cn(
                FONT_BIRCOLAGE_GROTESQUE.className,
                'text-3xl font-bold text-accent-100'
              )}
            >
              <span className="sr-only">Forms Notamess</span>
              Forms
            </Link>
            <nav className="hidden sm:block md:absolute md:top-1/2 md:left-1/2 md:-translate-x-1/2 md:-translate-y-1/2 md:transform">
              <div className="flex items-center gap-10 font-medium">
                {/* <Link className='px-2 py-1 text-gray-900' href='#solutions'>
                Solutions
              </Link> */}
              </div>
            </nav>
            <Link
              href={'/waitlist'}
              className={cn(buttonVariants({ variant: 'shadow' }), '')}
            >
              Join Waitlist
            </Link>
            {/* <div className="flex items-center space-x-4">
            {!user ? (
              <div className="flex items-center space-x-4">
                <Link
                  href={'/login'}
                  className={cn(buttonVariants({ variant: 'shadow' }), '')}
                >
                  Login
                </Link>
                <Link
                  href={'/create-account'}
                  className={cn(
                    buttonVariants({ variant: 'shadow_accent' }),
                    ''
                  )}
                >
                  Get Started
                </Link>
              </div>
            ) : profile ? (
              <div className="flex flex-row items-center space-x-2">
                <Link
                  href={`/${profile.username}`}
                  className={cn(
                    buttonVariants({ variant: 'shadow_accent' }),
                    ''
                  )}
                >
                  Dashboard
                </Link>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant={'ghost'}
                      aria-label="Open account menu"
                      className="w-full px-2 h-fit space-x-2 justify-between flex items-center"
                    >
                      <div
                        className={cn(
                          buttonVariants({ size: 'sm', variant: 'shadow' }),
                          'p-1 h-fit'
                        )}
                      >
                        <UserAvatar
                          size="sm"
                          avatarUrl={profile?.avatar_url}
                          fallbackText={profile?.full_name || ''}
                        />
                      </div>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent
                    align="end"
                    className={cn(
                      FONT_JETBRAINS_MONO.className,
                      'max-w-60 w-48 rounded-2xl'
                    )}
                  >
                    <DropdownMenuLabel className="flex flex-col gap-3 pt-2">
                      <div className="flex min-w-0 flex-col">
                        <span className="text-neutral-950 truncate text-lg font-medium">
                          {profile?.full_name}
                        </span>
                        <span
                          className={cn(
                            FONT_BIRCOLAGE_GROTESQUE.className,
                            'text-neutral-500 truncate text-xs font-normal'
                          )}
                        >
                          {profile?.email}
                        </span>
                      </div>
                    </DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuGroup>
                      <DropdownMenuItem asChild>
                        <Link
                          href={`/`}
                          className="rounded-xl w-full flex items-center  transition-all duration-200 justify-start gap-2 px-2 py-2 h-fit"
                        >
                          <HomeIcon size={16} className="opacity-60" />
                          <span className="text-sm uppercase font-medium">
                            Home
                          </span>
                        </Link>
                      </DropdownMenuItem>
                    </DropdownMenuGroup>
                    <DropdownMenuSeparator />
                    <DropdownMenuGroup>
                      <DropdownMenuItem asChild>
                        <Link
                          href={`/${profile?.username}/settings`}
                          className="rounded-xl w-full flex items-center  transition-all duration-200 justify-start gap-2 px-2 py-2 h-fit"
                        >
                          <Cog size={16} className="opacity-60" />
                          <span className="text-sm uppercase font-medium">
                            Settings
                          </span>
                        </Link>
                      </DropdownMenuItem>
                    </DropdownMenuGroup>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem
                      className={cn(
                        'rounded-xl w-full flex items-center shadow-default *:text-red-900 hover:text-red-900 transition-all duration-200 justify-start gap-2 px-2 py-2 h-fit'
                      )}
                      onClick={() =>
                        toast.promise(handleLogOut(), {
                          loading: 'Logging Out',
                          success: () => 'Logged Out',
                          error: (err: any) => `Error: ${err.message}`,
                        })
                      }
                    >
                      <DoorOpenIcon
                        size={16}
                        className="opacity-60 *:text-red-900 hover:text-neutral-600"
                      />
                      <span
                        className={cn(
                          FONT_JETBRAINS_MONO.className,
                          'font-bold uppercase'
                        )}
                      >
                        Log Out
                      </span>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            ) : (
              // If user exists but profile doesn't, show login button
              <div className="flex items-center space-x-4">
                <Link
                  href={'/login'}
                  className={cn(buttonVariants({ variant: 'shadow' }), '')}
                >
                  Login
                </Link>
              </div>
            )}
          </div> */}
          </div>
          <nav
            className={cn(
              'mt-6 flex flex-col gap-6 text-lg ease-in-out will-change-transform sm:hidden',
              open ? '' : 'hidden'
            )}
          >
            <ul className="space-y-4 font-medium">
              <li onClick={() => setOpen(false)}>
                <Link href="#solutions">Solutions</Link>
              </li>
              <li onClick={() => setOpen(false)}>
                <Link href="#farm-management">Farm Management</Link>
              </li>
              <li onClick={() => setOpen(false)}>
                <Link href="#solar-analytics">Analytics</Link>
              </li>
            </ul>
            <Button variant="secondary" className="text-lg">
              Get a quote
            </Button>
          </nav>
        </div>
      </div>
    </header>
  );
}
