/**
 * Notification Service
 *
 * Handles creation, management, and delivery of notifications including:
 * - Database notification storage
 * - Email notification delivery
 * - Toast notification display
 * - User preference management
 *
 * Fixed issues:
 * - Type safety for email settings JSON parsing
 * - Null safety for user email handling
 * - Toast options compatibility with sonner
 * - Input validation for all methods
 */

import { supabaseClient } from '@/lib/supabase/client';
import { sendEmail } from '@/lib/emails';
import { toast } from 'sonner';
import { triggerNotificationUpdate } from '@/lib/notification-events';
import { Notification } from '@/lib/types/database-modules';

// Define email settings interface
interface EmailNotificationSettings {
  document_shared?: boolean;
  document_updated?: boolean;
  comment_added?: boolean;
  contract_signed?: boolean;
}

// Helper function to safely parse email settings
function parseEmailSettings(settings: any): EmailNotificationSettings | null {
  if (!settings || typeof settings !== 'object') {
    return null;
  }

  // If it's already a proper object, return it
  if (typeof settings === 'object' && !Array.isArray(settings)) {
    return settings as EmailNotificationSettings;
  }

  return null;
}

export interface NotificationOptions {
  type?: 'info' | 'success' | 'warning' | 'error';
  duration?: number;
  description?: string;
  action?: {
    label: string;
    onClick: () => void;
  };
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
  closeButton?: boolean;
}

export interface CreateNotificationParams {
  userId: string;
  title: string;
  content: string;
  type: string;
  actionUrl?: string;
  relatedId?: string;
  relatedType?: string;
  sendEmail?: boolean;
  emailParams?: {
    documentName?: string;
    notificationType?: 'shared' | 'updated' | 'commented' | 'signed';
    senderName?: string;
    message?: string;
  };
}

class NotificationService {
  /**
   * Create a notification in the database and optionally send an email
   */
  async createNotification(
    params: CreateNotificationParams
  ): Promise<Notification | null> {
    try {
      // Validate required parameters
      if (!params.userId || !params.title || !params.content || !params.type) {
        console.error('Missing required parameters for notification creation');
        return null;
      }
      // Get user email if sending email notification
      let userEmail = '';
      if (params.sendEmail) {
        const { data: userData, error: userError } = await supabaseClient
          .from('profiles')
          .select('email')
          .eq('id', params.userId)
          .single();

        if (userError) {
          console.error('Error fetching user email:', userError);
        } else if (userData && userData.email) {
          userEmail = userData.email;
        }
      }

      // Create notification in database
      const { data, error } = await supabaseClient
        .from('notifications')
        .insert({
          user_id: params.userId,
          title: params.title,
          content: params.content,
          type: params.type,
          action_url: params.actionUrl || null,
          related_id: params.relatedId || null,
          related_type: params.relatedType || null,
          read: false,
        })
        .select()
        .single();

      if (error) {
        console.error('Error creating notification:', error);
        return null;
      }

      // Show a toast notification in the browser environment
      if (typeof window !== 'undefined') {
        // Only show toast if the user is not currently viewing the notifications
        const notificationsOpen = document.querySelector(
          '[data-state="open"][aria-label="Notifications"]'
        );
        if (!notificationsOpen) {
          this.showToast(params.title, {
            type: 'info',
            description: params.content,
            action: params.actionUrl
              ? {
                  label: 'View',
                  onClick: () => {
                    window.location.href = params.actionUrl!;
                  },
                }
              : undefined,
          });
        }
      }

      // Send email notification if requested and we have the user's email
      // Only attempt to send emails on the server side
      if (
        params.sendEmail &&
        userEmail &&
        params.emailParams &&
        typeof window === 'undefined'
      ) {
        try {
          // Check if user has email notifications enabled
          const { data: settings, error: settingsError } = await supabaseClient
            .from('user_settings')
            .select('email_notifications')
            .eq('user_id', params.userId)
            .single();

          if (settingsError) {
            console.error('Error fetching user settings:', settingsError);
          } else if (settings && settings.email_notifications) {
            // Check if the specific notification type is enabled
            let shouldSendEmail = true;

            // Safely parse email settings
            const emailSettings = parseEmailSettings(
              settings.email_notifications
            );

            if (
              params.type === 'document' &&
              params.emailParams.notificationType &&
              emailSettings
            ) {
              switch (params.emailParams.notificationType) {
                case 'shared':
                  shouldSendEmail = emailSettings.document_shared !== false;
                  break;
                case 'updated':
                  shouldSendEmail = emailSettings.document_updated !== false;
                  break;
                case 'commented':
                  shouldSendEmail = emailSettings.comment_added !== false;
                  break;
                case 'signed':
                  shouldSendEmail = emailSettings.contract_signed !== false;
                  break;
              }
            }

            if (shouldSendEmail) {
              try {
                await sendEmail({
                  type: 'documentNotification',
                  email: userEmail,
                  documentLink: params.actionUrl || '',
                  documentName: params.emailParams.documentName || '',
                  notificationType:
                    params.emailParams.notificationType || 'updated',
                  senderName: params.emailParams.senderName || 'A user',
                  message: params.emailParams.message,
                });
                console.log('Email notification sent successfully');
              } catch (sendError) {
                console.error('Error in sendEmail function:', sendError);
              }
            }
          }
        } catch (emailError) {
          console.error('Error sending email notification:', emailError);
          // Continue even if email fails
        }
      } else if (params.sendEmail && typeof window !== 'undefined') {
        console.log('Email sending skipped in browser environment');
      }

      // Trigger notification update event to refresh UI
      triggerNotificationUpdate();

      return data;
    } catch (error) {
      console.error('Error in createNotification:', error);
      return null;
    }
  }

  /**
   * Mark a notification as read - simplified version without realtime
   */
  async markAsRead(notificationId: string): Promise<boolean> {
    try {
      // Validate input
      if (!notificationId || typeof notificationId !== 'string') {
        console.error('Invalid notification ID provided');
        return false;
      }
      // Simple direct update without toast promise
      const { error } = await supabaseClient
        .from('notifications')
        .update({ read: true, updated_at: new Date().toISOString() })
        .eq('id', notificationId);

      if (error) {
        console.error('Error marking notification as read:', error);
        toast.error(`Failed to mark as read: ${error.message}`);
        return false;
      }

      // Trigger notification update event to refresh UI
      triggerNotificationUpdate();

      // Don't show toast for better UX
      return true;
    } catch (error) {
      console.error('Error in markAsRead:', error);
      toast.error('Failed to mark as read');
      return false;
    }
  }

  /**
   * Mark a notification as unread - simplified version without realtime
   */
  async markAsUnread(notificationId: string): Promise<boolean> {
    try {
      // Validate input
      if (!notificationId || typeof notificationId !== 'string') {
        console.error('Invalid notification ID provided');
        return false;
      }
      // Simple direct update without toast promise
      const { error } = await supabaseClient
        .from('notifications')
        .update({ read: false, updated_at: new Date().toISOString() })
        .eq('id', notificationId);

      if (error) {
        console.error('Error marking notification as unread:', error);
        toast.error(`Failed to mark as unread: ${error.message}`);
        return false;
      }

      // Trigger notification update event to refresh UI
      triggerNotificationUpdate();

      // Don't show toast for better UX
      return true;
    } catch (error) {
      console.error('Error in markAsUnread:', error);
      toast.error('Failed to mark as unread');
      return false;
    }
  }

  /**
   * Mark all notifications as read for a user - simplified version without realtime
   */
  async markAllAsRead(userId: string): Promise<boolean> {
    try {
      // Simple direct update without toast promise
      const { error } = await supabaseClient
        .from('notifications')
        .update({ read: true })
        .eq('user_id', userId)
        .eq('read', false);

      if (error) {
        console.error('Error marking all notifications as read:', error);
        toast.error(`Failed to mark all as read: ${error.message}`);
        return false;
      }

      // Trigger notification update event to refresh UI
      triggerNotificationUpdate();

      toast.success('All notifications marked as read');
      return true;
    } catch (error) {
      console.error('Error in markAllAsRead:', error);
      toast.error('Failed to mark all as read');
      return false;
    }
  }

  /**
   * Delete a notification - simplified version without realtime
   */
  async deleteNotification(notificationId: string): Promise<boolean> {
    try {
      // Simple direct delete without toast promise
      const { error } = await supabaseClient
        .from('notifications')
        .delete()
        .eq('id', notificationId);

      if (error) {
        console.error('Error deleting notification:', error);
        toast.error(`Failed to delete notification: ${error.message}`);
        return false;
      }

      // Trigger notification update event to refresh UI
      triggerNotificationUpdate();

      toast.success('Notification deleted');
      return true;
    } catch (error) {
      console.error('Error in deleteNotification:', error);
      toast.error('Failed to delete notification');
      return false;
    }
  }

  /**
   * Delete all notifications for a user - simplified version without realtime
   */
  async deleteAllNotifications(userId: string): Promise<boolean> {
    try {
      // Simple direct delete without toast promise
      const { error } = await supabaseClient
        .from('notifications')
        .delete()
        .eq('user_id', userId);

      if (error) {
        console.error('Error deleting all notifications:', error);
        toast.error(`Failed to delete all notifications: ${error.message}`);
        return false;
      }

      // Trigger notification update event to refresh UI
      triggerNotificationUpdate();

      toast.success('All notifications deleted');
      return true;
    } catch (error) {
      console.error('Error in deleteAllNotifications:', error);
      toast.error('Failed to delete all notifications');
      return false;
    }
  }

  /**
   * Get unread notification count for a user
   */
  async getUnreadCount(userId: string): Promise<number> {
    try {
      // Validate input
      if (!userId || typeof userId !== 'string') {
        console.error('Invalid user ID provided');
        return 0;
      }
      const { error, count } = await supabaseClient
        .from('notifications')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', userId)
        .eq('read', false);

      if (error) {
        console.error('Error getting unread count:', error);
        console.error('Error details:', JSON.stringify(error, null, 2));
        throw new Error(`Failed to get unread count: ${error.message}`);
      }

      return count || 0;
    } catch (error) {
      console.error('Error in getUnreadCount:', error);
      if (error instanceof Error) {
        console.error('Error message:', error.message);
        console.error('Error stack:', error.stack);
      } else {
        console.error('Unknown error type:', typeof error);
      }
      // Return 0 as a fallback but log the error properly
      return 0;
    }
  }

  /**
   * Show a toast notification
   */
  showToast(message: string, options: NotificationOptions = {}): void {
    const { type = 'info', duration = 5000 } = options;

    // Create toast options compatible with sonner
    const toastOptions: any = {
      duration,
    };

    // Add description if provided
    if (options.description) {
      toastOptions.description = options.description;
    }

    // Add action if provided
    if (options.action) {
      toastOptions.action = options.action;
    }

    switch (type) {
      case 'success':
        toast.success(message, toastOptions);
        break;
      case 'error':
        toast.error(message, toastOptions);
        break;
      case 'warning':
        toast.warning(message, toastOptions);
        break;
      case 'info':
      default:
        toast(message, toastOptions);
    }
  }

  /**
   * Create a document shared notification
   */
  async documentShared(
    userId: string,
    documentId: string,
    documentName: string,
    senderName: string,
    message?: string
  ): Promise<Notification | null> {
    const baseUrl = typeof window !== 'undefined' ? window.location.origin : '';
    const actionUrl = `${baseUrl}/${userId}/documents/${documentId}`;

    return this.createNotification({
      userId,
      title: 'Document Shared',
      content: `${senderName} shared "${documentName}" with you`,
      type: 'document',
      actionUrl,
      relatedId: documentId,
      relatedType: 'document',
      sendEmail: true,
      emailParams: {
        documentName,
        notificationType: 'shared',
        senderName,
        message,
      },
    });
  }

  /**
   * Create a document updated notification
   */
  async documentUpdated(
    userId: string,
    documentId: string,
    documentName: string,
    senderName: string
  ): Promise<Notification | null> {
    const baseUrl = typeof window !== 'undefined' ? window.location.origin : '';
    const actionUrl = `${baseUrl}/${userId}/documents/${documentId}`;

    return this.createNotification({
      userId,
      title: 'Document Updated',
      content: `${senderName} updated "${documentName}"`,
      type: 'document',
      actionUrl,
      relatedId: documentId,
      relatedType: 'document',
      sendEmail: true,
      emailParams: {
        documentName,
        notificationType: 'updated',
        senderName,
      },
    });
  }

  /**
   * Create a comment added notification
   */
  async commentAdded(
    userId: string,
    documentId: string,
    documentName: string,
    senderName: string,
    comment: string
  ): Promise<Notification | null> {
    const baseUrl = typeof window !== 'undefined' ? window.location.origin : '';
    const actionUrl = `${baseUrl}/${userId}/documents/${documentId}`;

    // Create a more informative notification with comment content
    const truncatedComment =
      comment.length > 50 ? `${comment.substring(0, 50)}...` : comment;

    return this.createNotification({
      userId,
      title: 'New Comment',
      content: `${senderName} commented on "${documentName}": "${truncatedComment}"`,
      type: 'comment',
      actionUrl,
      relatedId: documentId,
      relatedType: 'document',
      sendEmail: true,
      emailParams: {
        documentName,
        notificationType: 'commented',
        senderName,
        message: comment,
      },
    });
  }

  /**
   * Create a document signed notification
   */
  async documentSigned(
    userId: string,
    documentId: string,
    documentName: string,
    senderName: string
  ): Promise<Notification | null> {
    const baseUrl = typeof window !== 'undefined' ? window.location.origin : '';
    const actionUrl = `${baseUrl}/${userId}/documents/${documentId}`;

    return this.createNotification({
      userId,
      title: 'Document Signed',
      content: `${senderName} signed "${documentName}"`,
      type: 'document',
      actionUrl,
      relatedId: documentId,
      relatedType: 'document',
      sendEmail: true,
      emailParams: {
        documentName,
        notificationType: 'signed',
        senderName,
      },
    });
  }

  /**
   * Create a document exported notification
   */
  async documentExported(
    userId: string,
    documentId: string,
    documentName: string,
    exportFormat: string = 'PDF'
  ): Promise<Notification | null> {
    const baseUrl = typeof window !== 'undefined' ? window.location.origin : '';
    const actionUrl = `${baseUrl}/${userId}/documents/${documentId}`;

    return this.createNotification({
      userId,
      title: 'Document Exported',
      content: `Your document "${documentName}" was exported as ${exportFormat}`,
      type: 'document',
      actionUrl,
      relatedId: documentId,
      relatedType: 'document',
      sendEmail: false, // No email for exports to avoid spam
    });
  }
}

export const notificationService = new NotificationService();
