'use client';

import { LawyerConsultationsList } from '@/components/lawyer/LawyerConsultationsList';
import { LawyerStats } from '@/components/lawyer/LawyerStats';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useLawyers, useLawyersExtended } from '@/lib/hooks';
import { userStore } from '@/lib/store/user';
import {
  BarChart,
  Calendar,
  Eye,
  FileText,
  Gavel,
  Repeat,
  Users,
} from 'lucide-react';
import Link from 'next/link';
import { useParams, useRouter } from 'next/navigation';
import { useEffect } from 'react';
import { toast } from 'sonner';

export default function LawyerDashboardPage() {
  const params = useParams();
  const username = params.username as string;
  const router = useRouter();
  const { profile } = userStore();

  const {
    loading,
    is<PERSON>awyer,
    lawyer<PERSON><PERSON><PERSON>le,
    lawyerConsultations,
    fetchLawyerProfile,
    fetchLawyerConsultations,
  } = useLawyers();

  // Get clients from the extended hook
  const { clients } = useLawyersExtended();

  useEffect(() => {
    if (profile && profile.user_role !== 'lawyer') {
      toast.error('Access denied', {
        description: 'You need to be a lawyer to access this page',
      });
      router.push(`/${username}`);
    } else if (profile) {
      // Only fetch if we have a profile
      fetchLawyerProfile().catch((error) => {
        console.error('Error fetching lawyer profile:', error);
        // Don't show error to user, just log it
      });
    }
  }, [profile, username, router, fetchLawyerProfile]);

  useEffect(() => {
    if (lawyerProfile) {
      fetchLawyerConsultations(lawyerProfile.id).catch((error) => {
        console.error('Error fetching lawyer consultations:', error);
        // Don't show error to user, just log it
      });
    }
  }, [lawyerProfile, fetchLawyerConsultations]);

  if (loading) {
    return (
      <div className="p-6">
        <h1 className="text-2xl font-bold mb-4">Lawyer Dashboard</h1>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  if (!isLawyer || !lawyerProfile) {
    return (
      <div className="p-6">
        <h1 className="text-2xl font-bold mb-4">Lawyer Dashboard</h1>
        <Card>
          <CardContent className="p-6">
            <div className="text-center">
              <Gavel className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <h2 className="text-xl font-semibold mb-2">
                {!isLawyer
                  ? 'Not a Lawyer Account'
                  : 'Lawyer Profile Not Found'}
              </h2>
              <p className="text-muted-foreground mb-4">
                {!isLawyer
                  ? 'You need to have a lawyer account to access this dashboard.'
                  : 'Your lawyer profile has not been set up yet. Please contact support.'}
              </p>
              <Button onClick={() => router.push(`/${username}`)}>
                Return to Home
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="flex flex-col md:flex-row items-start md:items-center justify-between mb-6 gap-4">
        <div>
          <h1 className="text-2xl font-bold">Lawyer Dashboard</h1>
          <p className="text-muted-foreground">
            Manage your consultations and legal services
          </p>
        </div>
        <div className="flex flex-wrap gap-3">
          <Button
            variant="outline"
            className="gap-2"
            onClick={() => router.push(`/${username}/lawyer`)}
          >
            <Eye className="h-4 w-4" />
            <span>View Public Profile</span>
          </Button>
          {!isLawyer && (
            <Button
              variant="outline"
              className="gap-2"
              onClick={() =>
                router.push(`/${username}/lawyer/client-dashboard`)
              }
            >
              <Users className="h-4 w-4" />
              <span>Client Dashboard</span>
            </Button>
          )}
          <Button
            className="gap-2"
            onClick={() => router.push(`/${username}/lawyer/availability`)}
          >
            <Calendar className="h-4 w-4" />
            <span>Set Availability</span>
          </Button>
        </div>
      </div>

      <div className="space-y-6">
        {lawyerProfile ? (
          <LawyerStats lawyerId={lawyerProfile.id} />
        ) : (
          <Card className="p-6">
            <div className="text-center">
              <p className="text-muted-foreground mb-4">
                Loading lawyer profile...
              </p>
            </div>
          </Card>
        )}

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card className="hover:shadow-md transition-shadow">
            <Link href={`/${username}/lawyer/dashboard/consultations`}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle>Consultations</CardTitle>
                  <Calendar className="h-5 w-5 text-primary" />
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground mb-4">
                  Manage all your client consultations and appointments
                </p>
                <div className="flex justify-between items-center">
                  <div className="text-2xl font-bold">
                    {
                      lawyerConsultations.filter(
                        (c) => c.status === 'scheduled'
                      ).length
                    }
                  </div>
                  <Badge variant="outline" className="bg-primary/10">
                    Upcoming
                  </Badge>
                </div>
              </CardContent>
            </Link>
          </Card>

          <Card className="hover:shadow-md transition-shadow">
            <Link href={`/${username}/lawyer/dashboard/documents`}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle>Documents</CardTitle>
                  <FileText className="h-5 w-5 text-primary" />
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground mb-4">
                  Review and manage client document submissions
                </p>
                <div className="flex justify-between items-center">
                  <div className="text-2xl font-bold">0</div>
                  <Badge variant="outline" className="bg-primary/10">
                    Pending
                  </Badge>
                </div>
              </CardContent>
            </Link>
          </Card>

          <Card className="hover:shadow-md transition-shadow">
            <Link href={`/${username}/lawyer/dashboard/profile`}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle>Profile</CardTitle>
                  <Users className="h-5 w-5 text-primary" />
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground mb-4">
                  Manage your professional profile and settings
                </p>
                <div className="flex justify-between items-center">
                  <div className="text-sm">
                    {lawyerProfile?.profile_complete
                      ? 'Complete'
                      : 'Incomplete'}
                  </div>
                  <Badge
                    variant="outline"
                    className={`${
                      lawyerProfile?.profile_complete
                        ? 'bg-green-50 text-green-700 border-green-200'
                        : 'bg-red-50 text-red-700 border-red-200'
                    }`}
                  >
                    {lawyerProfile?.profile_complete
                      ? 'Complete'
                      : 'Action Needed'}
                  </Badge>
                </div>
              </CardContent>
            </Link>
          </Card>

          <Card className="hover:shadow-md transition-shadow">
            <Link href={`/${username}/lawyer/clients`}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle>Clients</CardTitle>
                  <Users className="h-5 w-5 text-primary" />
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground mb-4">
                  Manage your client relationships and consultations
                </p>
                <div className="flex justify-between items-center">
                  <div className="text-2xl font-bold">
                    {clients?.length || 0}
                  </div>
                  <Badge variant="outline" className="bg-primary/10">
                    Total Clients
                  </Badge>
                </div>
              </CardContent>
            </Link>
          </Card>

          <Card className="hover:shadow-md transition-shadow">
            <Link href={`/${username}/lawyer/calendar`}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle>Calendar</CardTitle>
                  <Calendar className="h-5 w-5 text-primary" />
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground mb-4">
                  View and manage your consultation schedule
                </p>
                <div className="flex justify-between items-center">
                  <div className="text-2xl font-bold">
                    {
                      lawyerConsultations.filter(
                        (c) => c.status === 'scheduled'
                      ).length
                    }
                  </div>
                  <Badge variant="outline" className="bg-primary/10">
                    Upcoming
                  </Badge>
                </div>
              </CardContent>
            </Link>
          </Card>

          <Card className="hover:shadow-md transition-shadow">
            <Link href={`/${username}/lawyer/recurring-consultations`}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle>Recurring Consultations</CardTitle>
                  <Repeat className="h-5 w-5 text-primary" />
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground mb-4">
                  Set up and manage recurring consultation schedules
                </p>
                <div className="flex justify-between items-center">
                  <div className="text-2xl font-bold">New</div>
                  <Badge variant="outline" className="bg-primary/10">
                    Feature
                  </Badge>
                </div>
              </CardContent>
            </Link>
          </Card>

          <Card className="hover:shadow-md transition-shadow">
            <Link href={`/${username}/lawyer/reports`}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle>Reports & Analytics</CardTitle>
                  <BarChart className="h-5 w-5 text-primary" />
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground mb-4">
                  Generate reports and analyze your consultation data
                </p>
                <div className="flex justify-between items-center">
                  <div className="text-2xl font-bold">New</div>
                  <Badge variant="outline" className="bg-primary/10">
                    Feature
                  </Badge>
                </div>
              </CardContent>
            </Link>
          </Card>
        </div>

        <h2 className="text-xl font-semibold mt-6 mb-4">
          Upcoming Consultations
        </h2>
        <LawyerConsultationsList
          consultations={lawyerConsultations.filter(
            (c) => c.status === 'scheduled'
          )}
          limit={5}
          emptyMessage="No upcoming consultations"
        />
      </div>
    </div>
  );
}
