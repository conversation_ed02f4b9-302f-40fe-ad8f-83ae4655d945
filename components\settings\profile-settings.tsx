'use client';

import { useState } from 'react';
import { useUsers } from '@/lib/hooks';
import { Profile } from '@/lib/types/database-modules';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Skeleton } from '@/components/ui/skeleton';
import { toast } from 'sonner';
import { Loader2, Upload } from 'lucide-react';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { avatarService } from '@/lib/services/avatar-service';
// import { ImageCropper } from '@/components/ui/image-cropper';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';

interface ProfileSettingsProps {
  initialProfile: Profile | null;
}

const profileSchema = z.object({
  full_name: z.string().min(1, 'Full name is required'),
  username: z
    .string()
    .min(3, 'Username must be at least 3 characters')
    .regex(
      /^[a-z0-9_-]+$/,
      'Username can only contain lowercase letters, numbers, underscores, and hyphens'
    ),
  email: z.string().email('Invalid email address'),
  avatar_url: z.string().optional(),
});

type ProfileFormValues = z.infer<typeof profileSchema>;

export function ProfileSettings({ initialProfile }: ProfileSettingsProps) {
  const { updateProfile } = useUsers();
  const [avatarLoading, setAvatarLoading] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [cropperOpen, setCropperOpen] = useState(false);

  const form = useForm<ProfileFormValues>({
    resolver: zodResolver(profileSchema),
    defaultValues: {
      full_name: initialProfile?.full_name || '',
      username: initialProfile?.username || '',
      email: initialProfile?.email || '',
      avatar_url: initialProfile?.avatar_url || '',
    },
  });

  const onSubmit = async (values: ProfileFormValues) => {
    if (!initialProfile) return;

    const updatePromise = updateProfile.mutate({
      ...values,
      id: initialProfile.id,
    });

    toast.promise(updatePromise, {
      loading: 'Updating profile...',
      success: 'Profile updated successfully!',
      error: 'Failed to update profile',
    });

    try {
      await updatePromise;
    } catch (error) {
      console.error('Error updating profile:', error);
    }
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file || !initialProfile) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
      toast.error('Please select an image file');
      return;
    }

    // Validate file size (max 2MB)
    if (file.size > 2 * 1024 * 1024) {
      toast.error('Image size should be less than 2MB');
      return;
    }

    setSelectedFile(file);
    setCropperOpen(true);

    // Reset the file input so the same file can be selected again
    e.target.value = '';
  };

  const handleCropComplete = async (croppedImageBlob: Blob) => {
    if (!initialProfile) return;

    setAvatarLoading(true);

    try {
      // Convert blob to file
      const file = new File([croppedImageBlob], 'profile-image.jpg', {
        type: 'image/jpeg',
      });

      // Upload the file using the avatar service
      // This will use a consistent filename pattern and handle cleanup
      const avatarUrl = await avatarService.uploadAvatar(
        file,
        initialProfile.id
      );

      if (!avatarUrl) {
        throw new Error('Failed to upload avatar');
      }

      // Update the form value
      form.setValue('avatar_url', avatarUrl);

      // Also update the profile in the database
      await updateProfile.mutate({
        ...initialProfile,
        avatar_url: avatarUrl,
      });

      toast.success('Profile picture uploaded successfully');
    } catch (error) {
      console.error('Error uploading avatar:', error);
      toast.error('Failed to upload profile picture');
    } finally {
      setAvatarLoading(false);
      setSelectedFile(null);
    }
  };

  if (!initialProfile) {
    return (
      <Card>
        <CardHeader>
          <Skeleton className="h-6 w-1/3" />
          <Skeleton className="h-4 w-1/2" />
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Skeleton className="h-4 w-1/4" />
            <Skeleton className="h-10 w-full" />
          </div>
          <div className="space-y-2">
            <Skeleton className="h-4 w-1/4" />
            <Skeleton className="h-10 w-full" />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Form {...form}>
      {/* Image Cropper */}
      {/* <ImageCropper
        open={cropperOpen}
        onClose={() => setCropperOpen(false)}
        onCropComplete={handleCropComplete}
        imageFile={selectedFile}
        aspectRatio={1} // Square crop
      /> */}
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Profile Information</CardTitle>
            <CardDescription>
              Update your personal information and public profile
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="flex flex-col md:flex-row gap-6 items-start">
              <div className="flex flex-col items-center gap-2">
                <Avatar className="h-24 w-24">
                  <AvatarImage
                    src={
                      form.getValues('avatar_url') ||
                      `https://ui-avatars.com/api/?name=${encodeURIComponent(form.getValues('full_name'))}&background=random`
                    }
                    alt={form.getValues('full_name')}
                  />
                  <AvatarFallback>
                    {form
                      .getValues('full_name')
                      .split(' ')
                      .map((n) => n[0])
                      .join('')
                      .toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                <div className="relative">
                  <Input
                    type="file"
                    id="avatar"
                    className="hidden"
                    accept="image/*"
                    onChange={handleFileSelect}
                    disabled={avatarLoading}
                  />
                  <Button
                    variant="outline"
                    size="sm"
                    className="text-xs"
                    onClick={() => document.getElementById('avatar')?.click()}
                    disabled={avatarLoading}
                  >
                    {avatarLoading ? (
                      <Loader2 className="mr-1 h-3 w-3 animate-spin" />
                    ) : (
                      <Upload className="mr-1 h-3 w-3" />
                    )}
                    Change Avatar
                  </Button>
                </div>
              </div>

              <div className="flex-1 space-y-4">
                <FormField
                  control={form.control}
                  name="full_name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Full Name</FormLabel>
                      <FormControl>
                        <Input placeholder="John Doe" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Account Information</CardTitle>
            <CardDescription>
              Update your account details and login information
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <FormField
              control={form.control}
              name="username"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Username</FormLabel>
                  <FormControl>
                    <Input placeholder="johndoe" {...field} />
                  </FormControl>
                  <FormDescription>
                    Your unique username for NotAMess Forms. This will be used
                    in your profile URL.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Email Address</FormLabel>
                  <FormControl>
                    <Input placeholder="<EMAIL>" {...field} />
                  </FormControl>
                  <FormDescription>
                    Your email address is used for notifications and account
                    recovery.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
          <CardFooter className="flex justify-end">
            <Button type="submit" disabled={updateProfile.isLoading}>
              {updateProfile.isLoading && (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              )}
              Save Changes
            </Button>
          </CardFooter>
        </Card>
      </form>
    </Form>
  );
}
