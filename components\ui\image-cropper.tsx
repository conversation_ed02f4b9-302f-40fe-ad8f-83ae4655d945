'use client';

import { useState, useRef, useEffect } from 'react';
import ReactCrop, {
  Crop,
  PixelCrop,
  centerCrop,
  makeAspectCrop,
} from 'react-image-crop';
import 'react-image-crop/dist/ReactCrop.css';
import { Button } from '@/components/ui/button';
import {
  <PERSON>alog,
  DialogContent,
  DialogFooter,
  <PERSON>alogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Loader2 } from 'lucide-react';

interface ImageCropperProps {
  open: boolean;
  onClose: () => void;
  onCropComplete: (croppedImageBlob: Blob) => void;
  imageFile: File | null;
  aspectRatio?: number;
}

function centerAspectCrop(
  mediaWidth: number,
  mediaHeight: number,
  aspect: number
) {
  // Calculate the smallest possible crop that maintains the aspect ratio
  // and fits within the image dimensions
  const smallestDimension = Math.min(mediaWidth, mediaHeight);
  const cropSize =
    aspect >= 1
      ? { width: smallestDimension, height: smallestDimension / aspect }
      : { width: smallestDimension * aspect, height: smallestDimension };

  // Convert to percentage
  const percentCrop = {
    unit: '%',
    width: (cropSize.width / mediaWidth) * 100,
    height: (cropSize.height / mediaHeight) * 100,
    x: ((mediaWidth - cropSize.width) / 2 / mediaWidth) * 100,
    y: ((mediaHeight - cropSize.height) / 2 / mediaHeight) * 100,
  };

  return percentCrop;
}

export function ImageCropper({
  open,
  onClose,
  onCropComplete,
  imageFile,
  aspectRatio = 1, // Default to square crop
}: ImageCropperProps) {
  const [crop, setCrop] = useState<Crop>();
  const [completedCrop, setCompletedCrop] = useState<PixelCrop>();
  const [isLoading, setIsLoading] = useState(false);
  const [imgSrc, setImgSrc] = useState<string>('');
  const imgRef = useRef<HTMLImageElement>(null);

  useEffect(() => {
    if (imageFile) {
      const reader = new FileReader();
      reader.addEventListener('load', () => {
        setImgSrc(reader.result?.toString() || '');
      });
      reader.readAsDataURL(imageFile);
    }
  }, [imageFile]);

  // function onImageLoad(e: React.SyntheticEvent<HTMLImageElement>) {
  //   if (aspectRatio) {
  //     const { width, height } = e.currentTarget;
  //     const newCrop = centerAspectCrop(width, height, aspectRatio);
  //     setCrop(newCrop);

  //     // Also set the completed crop to ensure the Apply button is enabled
  //     // and the crop is properly initialized
  //     const pixelCrop: PixelCrop = {
  //       unit: 'px',
  //       x: Math.round((newCrop.x / 100) * width),
  //       y: Math.round((newCrop.y / 100) * height),
  //       width: Math.round((newCrop.width / 100) * width),
  //       height: Math.round((newCrop.height / 100) * height),
  //     };
  //     setCompletedCrop(pixelCrop);
  //   }
  // }

  const handleCropComplete = async () => {
    if (!imgRef.current || !completedCrop) {
      return;
    }

    setIsLoading(true);

    try {
      const croppedImageBlob = await getCroppedImg(
        imgRef.current,
        completedCrop,
        imageFile?.name || 'cropped.jpg'
      );

      onCropComplete(croppedImageBlob);
      onClose();
    } catch (error) {
      console.error('Error cropping image:', error);
    } finally {
      setIsLoading(false);
    }
  };

  function getCroppedImg(
    image: HTMLImageElement,
    pixelCrop: PixelCrop,
    fileName: string
  ): Promise<Blob> {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');

    if (!ctx) {
      throw new Error('No 2d context');
    }

    // Set canvas size to the cropped area
    canvas.width = pixelCrop.width;
    canvas.height = pixelCrop.height;

    // Draw the cropped image onto the canvas
    ctx.drawImage(
      image,
      pixelCrop.x,
      pixelCrop.y,
      pixelCrop.width,
      pixelCrop.height,
      0,
      0,
      pixelCrop.width,
      pixelCrop.height
    );

    // Convert canvas to blob
    return new Promise((resolve, reject) => {
      canvas.toBlob(
        (blob) => {
          if (!blob) {
            reject(new Error('Canvas is empty'));
            return;
          }
          resolve(blob);
        },
        'image/jpeg',
        0.95 // Quality
      );
    });
  }

  return (
    <Dialog open={open} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Crop Profile Image</DialogTitle>
        </DialogHeader>
        <div className="flex flex-col items-center justify-center space-y-4">
          {imgSrc ? (
            <div className="relative w-full flex justify-center">
              <ReactCrop
                crop={crop}
                onChange={(_, percentCrop) => setCrop(percentCrop)}
                onComplete={(c) => setCompletedCrop(c)}
                aspect={aspectRatio}
                circularCrop
                className="max-h-[400px] max-w-full"
                minWidth={50} // Minimum crop width in pixels
                minHeight={50} // Minimum crop height in pixels
              >
                <img
                  ref={imgRef}
                  alt="Crop me"
                  src={imgSrc}
                  // onLoad={onImageLoad}
                  className="max-h-[400px] max-w-full object-contain"
                  crossOrigin="anonymous" // Allow cropping cross-origin images
                />
              </ReactCrop>
            </div>
          ) : (
            <div className="h-64 w-full flex items-center justify-center bg-muted rounded-md">
              <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
            </div>
          )}
          <p className="text-sm text-muted-foreground">
            Drag to reposition. Resize using the corners.
          </p>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={isLoading}>
            Cancel
          </Button>
          <Button
            onClick={handleCropComplete}
            disabled={!completedCrop || isLoading}
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Processing...
              </>
            ) : (
              'Apply'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
