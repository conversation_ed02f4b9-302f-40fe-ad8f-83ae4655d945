/**
 * This file re-exports types from database-types.ts to provide a centralized place for all database-related types.
 * Use these types throughout the application instead of creating duplicate type definitions.
 */

import { Database, Enums, Json, Tables } from '@/lib/supabase/database-types';

// Re-export Json type and Database utilities
export type { Database, Enums, Json, Tables };

// ==========================================
// DOCUMENT QUERY TYPES
// ==========================================

export type GetAllDocumentsResult = {
  documents: Document[];
  nextCursor: string | null;
};

export type CreateDocumentInput = Partial<Document> & {
  document_type: string;
  owner_id: string;
  title: string;
};

export type DeleteDocumentInput = { id: string };
export type DeleteDocumentOutput = { success: boolean; id: string };

// ==========================================
// DOCUMENT TYPES
// ==========================================
// ==========================================

// Document types
export type Document = Database['public']['Tables']['documents']['Row'];
export type DocumentInsert =
  Database['public']['Tables']['documents']['Insert'];
export type DocumentUpdate =
  Database['public']['Tables']['documents']['Update'];

// Document-related types
export type DocumentComment =
  Database['public']['Tables']['document_comments']['Row'];
export type DocumentCommentInsert =
  Database['public']['Tables']['document_comments']['Insert'];
export type DocumentCommentUpdate =
  Database['public']['Tables']['document_comments']['Update'];

export type DocumentActivity =
  Database['public']['Tables']['document_activities']['Row'];
export type DocumentActivityInsert =
  Database['public']['Tables']['document_activities']['Insert'];
export type DocumentActivityUpdate =
  Database['public']['Tables']['document_activities']['Update'];

export type DocumentShareLink =
  Database['public']['Tables']['document_share_links']['Row'];
export type DocumentShareLinkInsert =
  Database['public']['Tables']['document_share_links']['Insert'];
export type DocumentShareLinkUpdate =
  Database['public']['Tables']['document_share_links']['Update'];

export type DocumentVersion =
  Database['public']['Tables']['document_versions']['Row'];
export type DocumentVersionInsert =
  Database['public']['Tables']['document_versions']['Insert'];
export type DocumentVersionUpdate =
  Database['public']['Tables']['document_versions']['Update'];

export type DocumentAttachment =
  Database['public']['Tables']['document_attachments']['Row'];
export type DocumentAttachmentInsert =
  Database['public']['Tables']['document_attachments']['Insert'];
export type DocumentAttachmentUpdate =
  Database['public']['Tables']['document_attachments']['Update'];

export type DocumentSummary =
  Database['public']['Views']['document_summaries']['Row'];

// Document enums
export enum DocumentType {
  CONTRACT = 'contract',
  FORM = 'form',
  LETTER = 'letter',
  AGREEMENT = 'agreement',
  REPORT = 'report',
  OTHER = 'other',
}

export enum DocumentStatus {
  DRAFT = 'draft',
  PUBLISHED = 'published',
  ARCHIVED = 'archived',
  TEMPLATE = 'template',
}

export enum DocumentActivityType {
  CREATED = 'created',
  UPDATED = 'updated',
  SHARED = 'shared',
  COMMENTED = 'commented',
  VIEWED = 'viewed',
  EXPORTED = 'exported',
  DELETED = 'deleted',
}

// ==========================================
// TEMPLATE TYPES
// ==========================================

// Template types
export type Template = Database['public']['Tables']['templates']['Row'];
export type TemplateInsert =
  Database['public']['Tables']['templates']['Insert'];
export type TemplateUpdate =
  Database['public']['Tables']['templates']['Update'];

// Template metadata types
export interface TemplateMetadata {
  id: string;
  version: string;
  name: string;
  description: string;
  category: 'contract' | 'agreement' | 'declaration' | 'notice' | 'form';
  jurisdiction: string;
  language: string;
  tags: string[];
  lastUpdated: Date;
  isPublished: boolean;
  baseTemplate?: string;
}

export interface TemplateSection {
  id: string;
  title: string;
  content: string;
  isRequired: boolean;
  order: number;
  variables: Variable[];
  condition?: {
    if: string;
    equals: string | number | boolean;
    then: boolean;
  };
  inheritsFrom?: string;
}

export interface Variable {
  name: string;
  type: 'text' | 'number' | 'date' | 'boolean';
  description: string;
  isRequired: boolean;
  validationRules?: {
    type: 'min' | 'max' | 'regex' | 'custom';
    value: number | string;
    message: string;
  }[];
}

export interface TemplateVersion {
  id: string;
  version: string;
  changes: {
    type: 'added' | 'modified' | 'removed';
    description: string;
    date: Date;
  }[];
  createdAt: Date;
  createdBy: string;
}

// ==========================================
// PROJECT TYPES
// ==========================================

// Project types
export type Project = Database['public']['Tables']['projects']['Row'];
export type ProjectInsert = Database['public']['Tables']['projects']['Insert'];
export type ProjectUpdate = Database['public']['Tables']['projects']['Update'];

export type ProjectMember =
  Database['public']['Tables']['project_members']['Row'];
export type ProjectMemberInsert =
  Database['public']['Tables']['project_members']['Insert'];
export type ProjectMemberUpdate =
  Database['public']['Tables']['project_members']['Update'];

export type ProjectTask = Database['public']['Tables']['project_tasks']['Row'];
export type ProjectTaskInsert =
  Database['public']['Tables']['project_tasks']['Insert'];
export type ProjectTaskUpdate =
  Database['public']['Tables']['project_tasks']['Update'];

export type ProjectMessage =
  Database['public']['Tables']['project_messages']['Row'];
export type ProjectMessageInsert =
  Database['public']['Tables']['project_messages']['Insert'];
export type ProjectMessageUpdate =
  Database['public']['Tables']['project_messages']['Update'];

// Project invitation type
export interface Invitation {
  id: string;
  project_id: string;
  email: string;
  role: string;
  status: string;
  invited_by: string;
  created_at: string;
  message?: string;
  expires_at?: string;
  project?: {
    id: string;
    name: string;
  };
}

export type ProjectInvitation = {
  id: string;
  project_id: string;
  email: string;
  role: string;
  status: string;
  invited_by: string;
  created_at: string;
  message?: string;
  expires_at?: string;
  project?: {
    id: string;
    name: string;
  };
};

// ==========================================
// USER TYPES
// ==========================================

// User types
export type Profile = Database['public']['Tables']['profiles']['Row'];
export type ProfileInsert = Database['public']['Tables']['profiles']['Insert'];
export type ProfileUpdate = Database['public']['Tables']['profiles']['Update'];

// User role enum from database
export type UserRole = Database['public']['Enums']['user_role'];

// Permission enum from database
export type Permission = Database['public']['Enums']['permission'];

// ==========================================
// NOTIFICATION TYPES
// ==========================================

// Notification types
export type Notification = Database['public']['Tables']['notifications']['Row'];
export type NotificationInsert =
  Database['public']['Tables']['notifications']['Insert'];
export type NotificationUpdate =
  Database['public']['Tables']['notifications']['Update'];

// ==========================================
// LAWYER TYPES
// ==========================================

// Lawyer types
export type Lawyer = Database['public']['Tables']['lawyers']['Row'];
export type LawyerInsert = Database['public']['Tables']['lawyers']['Insert'];
export type LawyerUpdate = Database['public']['Tables']['lawyers']['Update'];

export type LawyerConsultation =
  Database['public']['Tables']['lawyer_consultations']['Row'];
export type LawyerConsultationInsert =
  Database['public']['Tables']['lawyer_consultations']['Insert'];
export type LawyerConsultationUpdate =
  Database['public']['Tables']['lawyer_consultations']['Update'];

export type LawyerReview =
  Database['public']['Tables']['lawyer_reviews']['Row'];
export type LawyerReviewInsert =
  Database['public']['Tables']['lawyer_reviews']['Insert'];
export type LawyerReviewUpdate =
  Database['public']['Tables']['lawyer_reviews']['Update'];

export type LawyerDocumentReview =
  Database['public']['Tables']['lawyer_document_reviews']['Row'];
export type LawyerDocumentReviewInsert =
  Database['public']['Tables']['lawyer_document_reviews']['Insert'];
export type LawyerDocumentReviewUpdate =
  Database['public']['Tables']['lawyer_document_reviews']['Update'];

export type LawyerDashboardStats =
  Database['public']['Views']['lawyer_dashboard_stats']['Row'];
export type ClientConsultationMetrics =
  Database['public']['Views']['client_consultation_metrics']['Row'];
export type ConsultationMetrics =
  Database['public']['Views']['consultation_metrics']['Row'];
export type MonthlyConsultationStats =
  Database['public']['Views']['monthly_consultation_stats']['Row'];

// ==========================================
// RECURRING CONSULTATION TYPES
// ==========================================

export type RecurringConsultation =
  Database['public']['Tables']['recurring_consultations']['Row'];
export type RecurringConsultationInsert =
  Database['public']['Tables']['recurring_consultations']['Insert'];
export type RecurringConsultationUpdate =
  Database['public']['Tables']['recurring_consultations']['Update'];

export type RecurringFrequency = 'weekly' | 'biweekly' | 'monthly';
export type ConsultationType = 'video' | 'document';

export interface RecurringConsultationWithJoins {
  id: string;
  lawyer_id: string;
  user_id: string;
  title: string;
  description?: string;
  day_of_week: number; // 0-6, where 0 is Sunday
  start_time: string; // HH:MM:SS format
  duration_minutes: number;
  frequency: RecurringFrequency;
  start_date: string; // YYYY-MM-DD format
  end_date?: string; // YYYY-MM-DD format
  document_id?: string;
  consultation_type: ConsultationType;
  location?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;

  // Joined data
  lawyer?: {
    id: string;
    full_name: string;
    email: string;
    avatar_url?: string;
    specialization?: string;
  };
  client?: {
    id: string;
    full_name: string;
    email: string;
    avatar_url?: string;
  };
  document?: {
    id: string;
    title: string;
  };
}

export interface RecurringConsultationFormData {
  title: string;
  description?: string;
  day_of_week: number;
  start_time: string;
  duration_minutes: number;
  frequency: RecurringFrequency;
  start_date: Date;
  end_date?: Date;
  document_id?: string;
  consultation_type: ConsultationType;
  location?: string;
  lawyer_id?: string;
  client_id?: string;
}

export interface GeneratedConsultation {
  id?: string;
  recurring_id?: string;
  consultation_id?: string;
  consultation_date?: string;
  start_time?: string;
  end_time?: string;
  status?: string;
  created_at?: string;
}

// ==========================================
// CALENDAR INTEGRATION TYPES
// ==========================================

export type CalendarIntegration =
  Database['public']['Tables']['calendar_integrations']['Row'];
export type CalendarIntegrationInsert =
  Database['public']['Tables']['calendar_integrations']['Insert'];
export type CalendarIntegrationUpdate =
  Database['public']['Tables']['calendar_integrations']['Update'];

export interface CalendarEvent {
  id: string;
  title: string;
  description?: string;
  start: Date;
  end: Date;
  allDay?: boolean;
  color?: 'sky' | 'amber' | 'violet' | 'rose' | 'emerald' | 'orange';
  location?: string;
  consultationType?: 'video' | 'document';
  status?: 'scheduled' | 'confirmed' | 'completed' | 'cancelled';
  clientId?: string;
  lawyerId?: string;
  documentId?: string;
  client?: {
    id: string;
    full_name: string;
    email: string;
    avatar_url?: string | null;
  };
  lawyer?: {
    id: string;
    full_name: string;
    email: string;
    avatar_url?: string | null;
    specialization?: string[];
  };
}

export type CalendarView = 'month' | 'week' | 'day' | 'agenda';

export interface TimeSlot {
  start: Date;
  end: Date;
  isAvailable: boolean;
}

export interface AvailabilitySettings {
  dayOfWeek: number; // 0-6, where 0 is Sunday
  isAvailable: boolean;
  startTime: string; // HH:MM format
  endTime: string; // HH:MM format
}

// ==========================================
// REPORTING TYPES
// ==========================================

export type ReportType = 'performance' | 'client' | 'time_slot' | 'monthly';
export type ReportFrequency = 'weekly' | 'monthly' | 'quarterly'; // Aligned with database schema
export type MetricType =
  | 'total_consultations'
  | 'completed_consultations'
  | 'cancelled_consultations'
  | 'upcoming_consultations'
  | 'document_consultations'
  | 'video_consultations'
  | 'recurring_consultations'
  | 'total_minutes'
  | 'avg_duration_minutes'
  | 'completion_rate'
  | 'cancellation_rate';

export interface ReportSettings {
  id: string;
  user_id: string;
  report_type: string;
  is_enabled: boolean | null;
  email_recipients: string[] | null;
  include_metrics: string[] | null;
  created_at: string | null;
  updated_at: string | null;
}

export interface SavedReport {
  id: string;
  user_id: string;
  report_name: string;
  report_type: string;
  report_parameters: any;
  report_data: any;
  created_at: string | null;
  updated_at: string | null;
}

export interface PerformanceSummary {
  lawyer_id: string;
  lawyer_name: string;
  lawyer_specialization: string;
  total_consultations: number;
  completed_consultations: number;
  cancelled_consultations: number;
  upcoming_consultations: number;
  document_consultations: number;
  video_consultations: number;
  recurring_consultations: number;
  total_minutes: number;
  avg_duration_minutes: number;
  completion_rate: number;
  cancellation_rate: number;
}

export interface MonthlyTrend {
  year: number;
  month: number;
  total_consultations: number;
  completed_consultations: number;
  cancelled_consultations: number;
  total_minutes: number;
}

export interface TimeSlotData {
  day_of_week: number;
  hour: number;
  consultation_count: number;
}

export interface ClientData {
  client_id: string;
  client_name: string;
  consultation_count: number;
}

export interface PerformanceReport {
  performance_summary: PerformanceSummary;
  monthly_trend: MonthlyTrend[];
  popular_time_slots: TimeSlotData[];
  top_clients: ClientData[];
  report_period: {
    start_date: string;
    end_date: string;
  };
}

export interface ReportDateRange {
  startDate: Date;
  endDate: Date;
  from?: Date | undefined;
  to?: Date | undefined;
}

// ==========================================
// USER SETTINGS AND SUBSCRIPTION TYPES
// ==========================================

// Note: These types are already defined later in the file
// We're keeping the section header for organization

// ==========================================
// LAWYER CONSULTATION MESSAGE TYPES
// ==========================================

/**
 * Lawyer consultation message interface
 */
export interface LawyerConsultationMessage {
  id: string;
  consultation_id: string;
  sender_id: string;
  message: string;
  attachment_url?: string;
  attachment_name?: string;
  attachment_type?: string;
  is_read: boolean;
  created_at: string;
  updated_at: string;
  sender?: {
    id: string;
    full_name: string;
    email: string;
    avatar_url?: string;
  };
}

/**
 * Extended lawyer consultation interface with additional properties
 */
export interface LawyerConsultationExtended extends LawyerConsultation {
  client_id?: string; // For backward compatibility
  recurring_id?: string; // For recurring consultations
  title?: string; // Some consultations might not have a title
  start_time?: string; // Start time for the consultation
  end_time?: string; // End time for the consultation
  location?: string; // Location for the consultation
}

// ==========================================
// NOTIFICATION TYPES (EXTENDED)
// ==========================================

/**
 * Application notification interface with additional properties
 */
export interface AppNotification {
  id: string;
  user_id: string;
  type: string;
  title: string;
  content: string;
  read: boolean;
  action_url: string | null;
  related_id: string | null;
  related_type: string | null;
  created_at: string;
  updated_at: string;
}

// ==========================================
// CLIENT NOTES TYPES
// ==========================================

/**
 * Client note interface
 */
export interface ClientNote {
  id: string;
  client_id: string;
  lawyer_id: string;
  content: string;
  is_pinned: boolean | null;
  created_at: string | null;
  updated_at: string | null;
}

// ==========================================
// ORGANIZATION AND TEAM TYPES
// ==========================================

// Note: These types are already defined later in the file
// We're keeping the section header for organization

/**
 * Team with members interface - not defined elsewhere
 */
export interface TeamWithMembers {
  id: string;
  organization_id: string;
  name: string;
  description: string | null;
  permissions?: any;
  created_at: string;
  updated_at: string;
  members: Array<{
    id: string;
    team_id: string;
    user_id: string;
    role: string;
    created_at: string;
    full_name: string;
    email: string;
    avatar_url?: string;
  }>;
}

// ==========================================
// HELPER INTERFACE TYPES
// ==========================================

// Helper types for components
export interface Collaborator {
  id: string;
  email: string;
  full_name: string;
  avatar_url?: string;
  role: string;
  added_at: string;
}

export interface Comment {
  id: string;
  user_id: string;
  document_id: string;
  content: string;
  created_at: string;
  updated_at: string;
  user_full_name?: string;
  user_email?: string;
  user_avatar_url?: string;
  resolved: boolean;
}

export interface CollaboratorData {
  id: string;
  document_id: string;
  user_id: string;
  permission: string;
  created_at: string;
  user_email: string;
  user_full_name: string;
  user_avatar_url?: string;
}

// ==========================================
// ORGANIZATION TYPES
// ==========================================

export interface Organization {
  id: string;
  name: string;
  description: string | null;
  logo_url: string | null;
  primary_color: string | null;
  secondary_color: string | null;
  contact_email: string | null;
  subscription_level: string;
  billing_info: any;
  features: any;
  created_at: string;
  updated_at: string;
}

export interface OrganizationMember {
  id: string;
  organization_id: string;
  user_id: string;
  role: 'member' | 'admin' | 'owner';
  created_at: string;
  updated_at: string;
  user?: {
    id: string;
    email: string;
    full_name: string;
    avatar_url: string | null;
  };
}

export interface Team {
  id: string;
  organization_id: string;
  name: string;
  description: string | null;
  permissions: any;
  created_at: string;
  updated_at: string;
  members?: TeamMember[];
}

export interface TeamMember {
  id: string;
  team_id: string;
  user_id: string;
  role: 'member' | 'admin';
  created_at: string;
  updated_at: string;
  user?: {
    id: string;
    email: string;
    full_name: string;
    avatar_url: string | null;
  };
}

export interface OrganizationWithDetails extends Organization {
  members: OrganizationMember[];
  teams: Team[];
}

// ==========================================
// COLLABORATION TYPES
// ==========================================

// Collaboration Project types
export type CollaborationProject =
  Database['public']['Tables']['collaboration_projects']['Row'];
export type CollaborationProjectInsert =
  Database['public']['Tables']['collaboration_projects']['Insert'];
export type CollaborationProjectUpdate =
  Database['public']['Tables']['collaboration_projects']['Update'];

// Collaboration Project Member types
export type CollaborationProjectMember =
  Database['public']['Tables']['collaboration_project_members']['Row'];
export type CollaborationProjectMemberInsert =
  Database['public']['Tables']['collaboration_project_members']['Insert'];
export type CollaborationProjectMemberUpdate =
  Database['public']['Tables']['collaboration_project_members']['Update'];

// Collaboration Task types
export type CollaborationTask =
  Database['public']['Tables']['collaboration_tasks']['Row'];
export type CollaborationTaskInsert =
  Database['public']['Tables']['collaboration_tasks']['Insert'];
export type CollaborationTaskUpdate =
  Database['public']['Tables']['collaboration_tasks']['Update'];

// Collaboration Comment types
export type CollaborationComment =
  Database['public']['Tables']['collaboration_comments']['Row'];
export type CollaborationCommentInsert =
  Database['public']['Tables']['collaboration_comments']['Insert'];
export type CollaborationCommentUpdate =
  Database['public']['Tables']['collaboration_comments']['Update'];

// Collaboration Document types
export type CollaborationDocument =
  Database['public']['Tables']['collaboration_documents']['Row'];
export type CollaborationDocumentInsert =
  Database['public']['Tables']['collaboration_documents']['Insert'];
export type CollaborationDocumentUpdate =
  Database['public']['Tables']['collaboration_documents']['Update'];

// Extended types with additional properties
export interface CollaborationTaskWithAssignee extends CollaborationTask {
  assignee?: {
    id: string;
    full_name: string | null;
    avatar_url: string | null;
  };
}

export interface CollaborationCommentWithUser extends CollaborationComment {
  user?: {
    id: string;
    full_name: string | null;
    avatar_url: string | null;
  };
}

export interface CollaborationDocumentWithCreator
  extends CollaborationDocument {
  creator?: {
    id: string;
    full_name: string | null;
    avatar_url: string | null;
  };
}

export interface ProjectTaskWithAssignee extends ProjectTask {
  assignee?: {
    id: string;
    full_name: string | null;
    avatar_url: string | null;
  };
}

// ==========================================
// SETTINGS TYPES
// ==========================================

export interface UserSettings {
  id: string;
  user_id: string;
  theme: string;
  language: string;
  notifications_enabled: boolean;
  email_notifications: {
    document_updates: boolean;
    signatures: boolean;
    consultations: boolean;
    payments: boolean;
    marketing: boolean;
    comment_added: boolean;
    task_assigned: boolean;
    document_shared: boolean;
    document_updated: boolean;
  };
  display_preferences: {
    document_view: 'card' | 'table';
    sidebar_collapsed: boolean;
    show_document_previews: boolean;
    fontSize: 'small' | 'medium' | 'large';
    reduceMotion: boolean;
    highContrast: boolean;
    documentFont?: {
      fallback: string;
      className: string;
    };
  };
  export_preferences: {
    default_format: string;
    include_metadata: boolean;
    include_signatures: boolean;
  };
  security_preferences: {
    two_factor_enabled: boolean;
    login_notifications: boolean;
    session_timeout: number;
  };
  created_at: string;
  updated_at: string;
}

export interface SubscriptionInfo {
  subscription: {
    id: string;
    user_id: string;
    plan: 'free' | 'standard' | 'enterprise' | 'custom';
    status: 'active' | 'inactive' | 'canceled' | 'trial';
    trial_end?: string;
    next_billing_date?: string;
    current_period_end?: string;
    current_period_start?: string;
    cancel_at_period_end?: boolean;
    start_date: string;
    end_date?: string;
    auto_renew: boolean;
    payment_method?: {
      brand: string;
      last4: string;
    } | null;
    created_at: string;
    updated_at: string;
  };
  limits: {
    document_limit: number | null;
    storage_limit: number | null;
    collaborators_limit: number | null;
    lawyer_consultations_limit: number | null;
    smart_contracts_limit: number | null;
    features?: {
      advanced_templates?: boolean;
      smart_contract_deployment?: boolean;
      lawyer_consultations?: boolean;
      priority_support?: boolean;
    } | null;
  };
  usage: {
    documents_count: number;
    storage_used: number;
    collaborators_count: number;
    lawyer_consultations_count: number;
    smart_contracts_count: number;
  };
}

export interface UsageLimits {
  plan: 'Free' | 'Standard' | 'Enterprise' | 'Custom';
  price: number;
  billing_period: 'monthly' | 'yearly';
  features: string[];
  limits: {
    document_limit: number | null;
    storage_limit: number | null;
    collaborators_limit: number | null;
    lawyer_consultations_limit: number | null;
  };
}

// ==========================================
// LAWYER TYPES
// ==========================================

export interface SimpleLawyer {
  id: string;
  user_id: string;
  full_name: string;
  email: string;
  avatar_url?: string;
  specialization: string;
  bio?: string;
  average_rating: number;
  consultation_fee: number;
  consultation_duration_options: number[];
  created_at: string;
}

/**
 * Interface for lawyer client data
 */
export interface LawyerClient {
  id: string;
  full_name: string;
  email: string;
  avatar_url?: string;
  status?: string;
  consultation_count: number;
  last_consultation?: string;
  last_consultation_date?: string | null;
  created_at?: string;
  document?: {
    id: string;
    title: string;
  };
}

export interface SimpleConsultation {
  id: string;
  lawyer_id: string;
  user_id: string;
  document_id: string | null;
  consultation_date: string;
  duration_minutes: number;
  status: 'scheduled' | 'completed' | 'cancelled';
  notes: string | null;
  created_at: string;
  lawyer?: SimpleLawyer;
}

export interface ProjectComment {
  id: string;
  project_id: string;
  user_id: string;
  content: string;
  created_at: string;
  updated_at: string;
  task_id?: string | null;
  user?: {
    id: string;
    full_name: string | null;
    avatar_url: string | null;
  } | null;
}

export interface ProjectDocument {
  id: string;
  project_id: string;
  document_id: string;
  added_by: string | null;
  created_at: string;
  updated_at: string;
  document?: {
    id: string;
    title: string;
    description: string | null;
    document_type: string;
    status: string;
  } | null;
}

export interface ReportFilters {
  dateRange: ReportDateRange;
  statuses: string[];
  consultationTypes: string[];
  isRecurring: boolean | 'indeterminate';
  lawyerId?: string;
}

export interface ReportData {
  consultations: LawyerConsultation[];
  recurringConsultations: RecurringConsultation[];
  totalRevenue: number;
  totalConsultations: number;
  averageRevenue: number;
  statusBreakdown: Record<string, number>;
  typeBreakdown: Record<string, number>;
  recurringBreakdown: {
    recurring: number;
    oneTime: number;
  };
  revenueByDay: {
    date: string;
    revenue: number;
    count: number;
  }[];
}

export interface SaveReportFormData {
  name: string;
  description?: string;
  isPublic: boolean;
}

export interface SavedReportWithData extends SavedReport {
  reportData: ReportData;
}

/**
 * Chart.js data structure for reports
 */
export interface ChartData {
  labels: string[];
  datasets: {
    label: string;
    data: number[];
    backgroundColor: string;
    borderColor: string;
    borderWidth: number;
  }[];
}

export interface CreateOrganizationParams {
  name: string;
  description?: string;
  website?: string;
  logo_url?: string;
}

export interface UpdateOrganizationParams {
  name?: string;
  description?: string;
  website?: string;
  logo_url?: string;
}

export interface CreateTeamParams {
  name: string;
  description?: string;
  organization_id: string;
}

export interface UpdateTeamParams {
  name?: string;
  description?: string;
}

export interface AddTeamMemberParams {
  team_id: string;
  user_id: string;
  role: 'member' | 'admin';
}

export interface UpdateTeamMemberParams {
  role?: 'member' | 'admin';
}

// Using the OrganizationMember interface defined earlier

export interface OrganizationWithMembers extends Organization {
  members: OrganizationMember[];
}

// Using the TeamMember interface defined earlier
