'use client';
import React from 'react';

import { Button } from '@/components/ui/button';
import { BlurIn } from '@/components/ux/animations/blur-in';
import { TemplateWitIcon } from '@/components/ux/animations/icons/archive';
import { FormsWithIcon } from '@/components/ux/animations/icons/file-pen-line';
import { DashboardWithIcon } from '@/components/ux/animations/icons/layout-pannel-top';
import { SettingsWithIcon } from '@/components/ux/animations/icons/settings-gear';
import { FONT_BIRCOLAGE_GROTESQUE } from '@/lib/constants';
import { userStore } from '@/lib/store/user';
import { useGetUser } from '@/lib/supabase/auth/getUser';
import { cn } from '@/lib/utils';
import Logo from '@/public/FormsLogo.svg';
import Image from 'next/image';
import Link from 'next/link';
import { usePathname } from 'next/navigation';

type NavMenuProps = {
  setMenuOpen: React.Dispatch<React.SetStateAction<boolean>>;
  handleClose: () => void;
  handleAccount: () => void;
  // handleFeedback: () => void;
  // handleInfo: () => void;
  // handlePricing: () => void;
  className?: string;
};

export function HMenu({
  setMenuOpen,
  handleClose,
  // handleFeedback,
  // handleInfo,
  handleAccount,
  // handlePricing,
  className,
}: NavMenuProps) {
  const { profile, removeProfile, removeUser } = userStore();
  const { user } = useGetUser();
  const pathname = usePathname();
  const secondSegment = pathname?.split('/')[1] || '';

  const links = [
    {
      id: 4,
      content: (
        <Link
          href={`/${secondSegment}`}
          onClick={handleClose}
          className="text-lg font-medium text-neutral-400 transition-all flex items-center space-x-2 duration-300 hover:text-accent-200"
        >
          <DashboardWithIcon />
        </Link>
      ),
    },
    {
      id: 2,
      content: (
        <Link
          href={`/${secondSegment}/forms`}
          onClick={handleClose}
          className="text-lg font-medium text-neutral-400 transition-all flex items-center space-x-2 duration-300 hover:text-accent-200"
        >
          <FormsWithIcon />
        </Link>
      ),
    },
    {
      id: 1,
      content: (
        <Link
          href={`/${secondSegment}/templates`}
          onClick={handleClose}
          className="text-lg font-medium text-neutral-400 transition-all flex items-center space-x-2 duration-300 hover:text-accent-200"
        >
          <TemplateWitIcon />
        </Link>
      ),
    },
    {
      id: 3,
      content: (
        <Link
          href={`/${secondSegment}/settings`}
          onClick={handleClose}
          className="text-lg font-medium text-neutral-400 transition-all flex items-center space-x-2 duration-300 hover:text-accent-200 w-full"
        >
          <SettingsWithIcon />
        </Link>
      ),
    },
  ];

  const mainlinks = {
    auth: [
      {
        id: 1,
        content: (
          <Button variant={'shadow_accent'} onClick={handleAccount}>
            Account Details
          </Button>
        ),
        title: 'Login',
      },
    ],
    dashboard: [
      {
        id: 1,
        content: (
          <Link
            href={`/${profile?.username}/invoices`}
            onClick={() => setMenuOpen((s) => !s)}
            className="text-base font-medium text-neutral-400 transition-all duration-300 hover:text-accent-200"
          >
            Invoices
          </Link>
        ),
      },
      {
        id: 2,
        content: (
          <Link
            href={`/${profile?.username}`}
            onClick={() => setMenuOpen((s) => !s)}
            className="text-base font-medium text-neutral-400 transition-all duration-300 hover:text-accent-200"
          >
            Dashboard
          </Link>
        ),
      },

      {
        id: 3,
        content: (
          <Button variant={'shadow_accent'} onClick={handleAccount}>
            Account Details
          </Button>
        ),
      },
    ],
  };

  return (
    <BlurIn
      duration={0.3}
      className={cn(
        className,
        'relative flex h-[340px] w-[200px] flex-col items-center justify-end'
      )}
    >
      <div className="flex w-full items-center justify-between p-2">
        <div className="flex items-center space-x-1">
          <div className="w-8">
            <Image alt="logo" src={Logo} />
          </div>
          <h1
            className={cn(
              FONT_BIRCOLAGE_GROTESQUE.className,
              'text-center text-2xl font-medium text-accent-300'
            )}
          >
            Forms <br />{' '}
            {/* <span>
            Not<span className='text-accent-10'>a</span>mess
            </span> */}
          </h1>
        </div>
        <div className="flex items-center space-x-4">
          <Button variant={'shadow'} size={'icon'} className="size-8">
            {/* <CircleHelp className='size-4' /> */}
            <span className={cn(FONT_BIRCOLAGE_GROTESQUE.className, 'text-xl')}>
              ?
            </span>
          </Button>
        </div>
      </div>
      <div className="flex h-full w-full flex-col border-t border-accent-50/10">
        <div className="flex flex-col space-y- border-b border-accent-50/10 py-4 px-4">
          <div className="flex flex-col space-y-4">
            {links.map((l, i) => (
              <div key={i}>{l.content}</div>
            ))}
          </div>
        </div>
        <div className="mb-2 flex h-full w-full flex-col items-center justify-between">
          <div className="flex h-full flex-col items-center justify-center p-4">
            {mainlinks.auth.map((m) => (
              <div key={m.id}>{m.content}</div>
            ))}
          </div>
        </div>
      </div>
    </BlurIn>
  );
}
