'use client';

import { Document, DocumentSummary } from '@/lib/types/database-modules';

interface CacheItem<T> {
  data: T;
  timestamp: number;
  expiresAt: number;
}

class DocumentCacheService {
  private static instance: DocumentCacheService;
  private cache: Map<string, CacheItem<any>>;
  private debounceSaveTimeout: NodeJS.Timeout | null = null;
  private defaultTTL: number = 15 * 60 * 1000; // 15 minutes default TTL
  private documentSummariesKey = 'document_summaries';
  private documentPrefix = 'document_';
  private templatePrefix = 'template_';

  private constructor() {
    this.cache = new Map();

    // Initialize from localStorage if available
    if (typeof window !== 'undefined') {
      try {
        const savedCache = localStorage.getItem('document_cache');
        if (savedCache) {
          const parsed = JSON.parse(savedCache);
          Object.keys(parsed).forEach((key) => {
            this.cache.set(key, parsed[key]);
          });

          // Clean expired items
          this.cleanExpiredItems();
        }
      } catch (error) {
        console.error('Error initializing cache from localStorage:', error);
      }
    }
  }

  public static getInstance(): DocumentCacheService {
    if (!DocumentCacheService.instance) {
      DocumentCacheService.instance = new DocumentCacheService();
    }
    return DocumentCacheService.instance;
  }

  /**
   * Save the cache to localStorage
   */
  private saveToStorage(): void {
    if (typeof window === 'undefined') return;

    // Use debounced save to avoid excessive writes
    if (this.debounceSaveTimeout) {
      clearTimeout(this.debounceSaveTimeout);
    }

    this.debounceSaveTimeout = setTimeout(() => {
      try {
        const cacheObj: Record<string, CacheItem<any>> = {};
        this.cache.forEach((value, key) => {
          cacheObj[key] = value;
        });
        localStorage.setItem('document_cache', JSON.stringify(cacheObj));
      } catch (error) {
        console.error('Error saving cache to localStorage:', error);
      }
    }, 500); // Debounce for 500ms
  }

  /**
   * Clean expired items from the cache
   */
  private cleanExpiredItems(): void {
    const now = Date.now();
    let hasExpired = false;

    this.cache.forEach((item, key) => {
      if (item.expiresAt < now) {
        this.cache.delete(key);
        hasExpired = true;
      }
    });

    if (hasExpired) {
      this.saveToStorage();
    }
  }

  /**
   * Set an item in the cache
   */
  public set<T>(key: string, data: T, ttl: number = this.defaultTTL): void {
    const now = Date.now();
    this.cache.set(key, {
      data,
      timestamp: now,
      expiresAt: now + ttl,
    });

    this.saveToStorage();
  }

  /**
   * Get an item from the cache
   */
  public get<T>(key: string): T | null {
    // Only clean expired items occasionally to improve performance
    if (Math.random() < 0.1) {
      // 10% chance to clean on each get
      this.cleanExpiredItems();
    }

    const item = this.cache.get(key);
    if (!item) return null;

    // Check if this specific item is expired
    if (item.expiresAt < Date.now()) {
      this.cache.delete(key);
      return null;
    }

    return item.data as T;
  }

  /**
   * Check if an item exists in the cache and is not expired
   */
  public has(key: string): boolean {
    if (!this.cache.has(key)) return false;

    const item = this.cache.get(key);
    if (!item) return false;

    return item.expiresAt > Date.now();
  }

  /**
   * Remove an item from the cache
   */
  public remove(key: string): void {
    this.cache.delete(key);
    this.saveToStorage();
  }

  /**
   * Clear the entire cache
   */
  public clear(): void {
    this.cache.clear();
    if (typeof window !== 'undefined') {
      localStorage.removeItem('document_cache');
    }
  }

  /**
   * Cache document summaries
   */
  public cacheDocumentSummaries(summaries: DocumentSummary[]): void {
    // Sort summaries by updated_at date (newest first) for better performance
    const sortedSummaries = [...summaries].sort((a, b) => {
      // Handle null values for updated_at - treat null as oldest date
      const aDate = a.updated_at ? new Date(a.updated_at).getTime() : 0;
      const bDate = b.updated_at ? new Date(b.updated_at).getTime() : 0;

      return bDate - aDate;
    });

    this.set(this.documentSummariesKey, sortedSummaries);
  }

  /**
   * Get cached document summaries
   */
  public getCachedDocumentSummaries(): DocumentSummary[] | null {
    return this.get<DocumentSummary[]>(this.documentSummariesKey);
  }

  /**
   * Cache a single document
   */
  public cacheDocument(document: Document): void {
    this.set(`${this.documentPrefix}${document.id}`, document);
  }

  /**
   * Get a cached document
   */
  public getCachedDocument(id: string): Document | null {
    return this.get<Document>(`${this.documentPrefix}${id}`);
  }

  /**
   * Cache templates
   */
  public cacheTemplates(templates: Document[]): void {
    this.set('templates', templates);

    // Also cache each template individually
    templates.forEach((template) => {
      this.set(`${this.templatePrefix}${template.id}`, template);
    });
  }

  /**
   * Get cached templates
   */
  public getCachedTemplates(): Document[] | null {
    return this.get<Document[]>('templates');
  }

  /**
   * Get a cached template
   */
  public getCachedTemplate(id: string): Document | null {
    return this.get<Document>(`${this.templatePrefix}${id}`);
  }

  /**
   * Update a document in the cache if it exists
   */
  public updateDocumentIfCached(id: string, updates: Partial<Document>): void {
    const key = `${this.documentPrefix}${id}`;
    if (this.has(key)) {
      const document = this.get<Document>(key);
      if (document) {
        this.set(key, { ...document, ...updates });
      }
    }

    // Also update in document summaries if present
    const summaries = this.getCachedDocumentSummaries();
    if (summaries) {
      const updatedSummaries = summaries.map((summary) =>
        summary.id === id ? { ...summary, ...updates } : summary
      );
      this.cacheDocumentSummaries(updatedSummaries);
    }
  }

  /**
   * Remove a document from the cache
   */
  public removeDocumentFromCache(id: string): void {
    this.remove(`${this.documentPrefix}${id}`);

    // Also remove from document summaries if present
    const summaries = this.getCachedDocumentSummaries();
    if (summaries) {
      const updatedSummaries = summaries.filter((summary) => summary.id !== id);
      this.cacheDocumentSummaries(updatedSummaries);
    }
  }
}

export default DocumentCacheService;
