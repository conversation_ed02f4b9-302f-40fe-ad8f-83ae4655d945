import { supabaseClient } from '@/lib/supabase/client';
import {
  waitlistStore,
  WaitlistEntry,
  WaitlistStats,
} from '@/lib/store/waitlist';
import { useCallback } from 'react';

export function useWaitlist() {
  const {
    isOnWaitlist,
    userWaitlistEntry,
    stats,
    isLoading,
    error,
    setIsOnWaitlist,
    setUserWaitlistEntry,
    setStats,
    setLoading,
    setError,
    reset,
  } = waitlistStore();

  const joinWaitlist = useCallback(
    async (data: {
      full_name: string;
      email: string;
      role: 'user' | 'lawyer';
    }) => {
      return new Promise<boolean>(async (resolve, reject) => {
        try {
          setLoading(true);
          setError(null);

          const { data: result, error } = await supabaseClient
            .from('waitlist')
            .insert(data)
            .select()
            .single();

          if (error) {
            if (error.code === '23505') {
              // Unique constraint violation (email already exists)
              const errorMessage = 'This email is already on our waitlist!';
              setError(errorMessage);
              reject(new Error(errorMessage));
              return;
            }
            throw error;
          }
          setIsOnWaitlist(true);
          setUserWaitlistEntry(result as WaitlistEntry);
          resolve(true);
        } catch (err) {
          let errorMessage = 'Failed to join waitlist';

          if (err instanceof Error) {
            errorMessage = err.message;
          } else if (err && typeof err === 'object') {
            // Handle Supabase error objects
            if ('message' in err && typeof err.message === 'string') {
              errorMessage = err.message;
            } else if ('error' in err && typeof err.error === 'string') {
              errorMessage = err.error;
            } else {
              errorMessage = JSON.stringify(err);
            }
          } else if (typeof err === 'string') {
            errorMessage = err;
          }

          setError(errorMessage);
          reject(new Error(errorMessage));
        } finally {
          setLoading(false);
        }
      });
    },
    [setError, setIsOnWaitlist, setUserWaitlistEntry, setLoading]
  );

  const checkWaitlistStatus = useCallback(
    async (email: string) => {
      return new Promise<boolean>(async (resolve, reject) => {
        try {
          setError(null);

          const { data, error } = await supabaseClient
            .from('waitlist')
            .select('*')
            .eq('email', email)
            .single();

          if (error && error.code !== 'PGRST116') {
            // PGRST116 is "not found" error, which is expected if user is not on waitlist
            throw error;
          }

          if (data) {
            setIsOnWaitlist(true);
            setUserWaitlistEntry(data as WaitlistEntry);
          } else {
            setIsOnWaitlist(false);
            setUserWaitlistEntry(null);
          }

          resolve(!!data);
        } catch (err) {
          const errorMessage =
            err instanceof Error
              ? err.message
              : 'Failed to check waitlist status';
          setError(errorMessage);
          reject(new Error(errorMessage));
        }
      });
    },
    [setError, setIsOnWaitlist, setUserWaitlistEntry]
  );

  const fetchWaitlistStats = useCallback(async () => {
    return new Promise<WaitlistStats | null>(async (resolve, reject) => {
      try {
        setError(null);

        const response = await fetch('/api/waitlist');
        if (!response.ok) {
          throw new Error('Failed to fetch waitlist stats');
        }

        const { stats: fetchedStats } = await response.json();
        setStats(fetchedStats);
        resolve(fetchedStats);
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : 'Failed to fetch waitlist stats';
        setError(errorMessage);
        reject(new Error(errorMessage));
      }
    });
  }, [setError, setStats]);

  const removeFromWaitlist = useCallback(
    async (email: string) => {
      return new Promise<boolean>(async (resolve, reject) => {
        try {
          setError(null);

          const { error } = await supabaseClient
            .from('waitlist')
            .delete()
            .eq('email', email);

          if (error) {
            throw error;
          }

          setIsOnWaitlist(false);
          setUserWaitlistEntry(null);
          resolve(true);
        } catch (err) {
          const errorMessage =
            err instanceof Error
              ? err.message
              : 'Failed to remove from waitlist';
          setError(errorMessage);
          reject(new Error(errorMessage));
        }
      });
    },
    [setError, setIsOnWaitlist, setUserWaitlistEntry]
  );

  return {
    // State
    isOnWaitlist,
    userWaitlistEntry,
    stats,
    isLoading,
    error,

    // Actions
    joinWaitlist,
    checkWaitlistStatus,
    fetchWaitlistStats,
    removeFromWaitlist,
    reset,
  };
}
